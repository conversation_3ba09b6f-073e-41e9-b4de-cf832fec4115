// dllmain.cpp : Implementation of DllMain.

#include "pch.h"
#include "framework.h"
#include "resource.h"
#include "MyCom_i.h"
#include "dllmain.h"
#include "compreg.h"
#include "xdlldata.h"

CMyComModule _AtlModule;

class CMyComApp : public CWinApp
{
public:

// Overrides
	virtual BOOL InitInstance();
	virtual int ExitInstance();

	DECLARE_MESSAGE_MAP()
};

BEGIN_MESSAGE_MAP(CMyComApp, CWinApp)
END_MESSAGE_MAP()

CMyComApp theApp;

BOOL CMyComApp::InitInstance()
{
#ifdef _MERGE_PROXYSTUB
	if (!PrxDllMain(m_hInstance, DLL_PROCESS_ATTACH, nullptr))
		return FALSE;
#endif
	return CWinApp::InitInstance();
}

int CMyComApp::ExitInstance()
{
	return CWinApp::ExitInstance();
}
