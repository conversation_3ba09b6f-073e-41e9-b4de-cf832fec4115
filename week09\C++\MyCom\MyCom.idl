// MyCom.idl : IDL source for MyCom
//

// This file will be processed by the MIDL tool to
// produce the type library (MyCom.tlb) and marshalling code.

import "oaidl.idl";
import "ocidl.idl";

[
	object,
	uuid(a817e7a2-43fa-11d0-9e44-00aa00b6770a),
	dual,
	pointer_default(unique)
]
interface IComponentRegistrar : IDispatch
{
	[id(1)]	HRESULT Attach([in] BSTR bstrPath);
	[id(2)]	HRESULT RegisterAll();
	[id(3)]	HRESULT UnregisterAll();
	[id(4)]	HRESULT GetComponents([out] SAFEARRAY(BSTR)* pbstrCLSIDs, [out] SAFEARRAY(BSTR)* pbstrDescriptions);
	[id(5)]	HRESULT RegisterComponent([in] BSTR bstrCLSID);
	[id(6)] HRESULT UnregisterComponent([in] BSTR bstrCLSID);
};

[
	uuid(b492002d-9e19-4897-a076-d783833e4543),
	version(1.0),
	custom(a817e7a1-43fa-11d0-9e44-00aa00b6770a,"{cb34f019-2e6b-4c04-b4e6-cd257e292068}")
]
library MyComLib
{
	importlib("stdole2.tlb");
	[
		uuid(cb34f019-2e6b-4c04-b4e6-cd257e292068)
	]
	coclass CompReg
	{
		[default] interface IComponentRegistrar;
	};
};

