param(
    [Parameter(Position=0)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Debug",
    
    [Parameter()]
    [ValidateSet("Win32", "x64")]
    [string]$Platform = "Win32",
    
    [switch]$Clean,
    [switch]$NoRegister
)

# Set error handling
$ErrorActionPreference = "Stop"

Write-Host "====================================" -ForegroundColor Cyan
Write-Host "MyCom ATL+COM DLL - CMake Build Script" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Check administrator privileges
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not $NoRegister -and -not (Test-Administrator)) {
    Write-Host "Error: Administrator privileges required for COM component registration!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator, or use -NoRegister parameter." -ForegroundColor Yellow
    exit 1
}

# Display build parameters
Write-Host "Build Configuration: $Configuration" -ForegroundColor Green
Write-Host "Target Platform: $Platform" -ForegroundColor Green
Write-Host "Administrator: $(if (Test-Administrator) { 'Yes' } else { 'No' })" -ForegroundColor Green

# Clean build directory
if ($Clean -and (Test-Path "build")) {
    Write-Host "Cleaning build directory..." -ForegroundColor Yellow
    Remove-Item "build" -Recurse -Force
}

# Create build directory
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" | Out-Null
}

# Enter build directory
Set-Location "build"

try {
    # Check if CMake is available
    try {
        $cmakeVersion = cmake --version
        Write-Host "CMake detected: $($cmakeVersion[0])" -ForegroundColor Green
    }
    catch {
        Write-Host "Error: CMake not found! Please install CMake and add to PATH." -ForegroundColor Red
        exit 1
    }

    # Check if Visual Studio is available
    $vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vsWhere) {
        $vsInfo = & $vsWhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property displayName
        if ($vsInfo) {
            Write-Host "Visual Studio detected: $vsInfo" -ForegroundColor Green
        }
    }

    # Configure CMake project
    Write-Host ""
    Write-Host "Configuring CMake project..." -ForegroundColor Yellow
    
    $cmakeArgs = @(
        "-G", "Visual Studio 17 2022",
        "-A", $Platform,
        "-DCMAKE_BUILD_TYPE=$Configuration"
    )
    
    Write-Host "Executing: cmake $($cmakeArgs -join ' ') .." -ForegroundColor Gray
    
    & cmake @cmakeArgs ".."
    
    if ($LASTEXITCODE -ne 0) {
        throw "CMake configuration failed with exit code: $LASTEXITCODE"
    }

    # Build project
    Write-Host ""
    Write-Host "Building project..." -ForegroundColor Yellow
    
    $buildArgs = @(
        "--build", ".",
        "--config", $Configuration,
        "--verbose"
    )
    
    Write-Host "Executing: cmake $($buildArgs -join ' ')" -ForegroundColor Gray
    
    & cmake @buildArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "Project build failed with exit code: $LASTEXITCODE"
    }

    # Check output files
    $dllPath = "bin\$Configuration\MyCom.dll"
    $tlbPath = "MyCom.tlb"
    
    if (Test-Path $dllPath) {
        Write-Host ""
        Write-Host "====================================" -ForegroundColor Green
        Write-Host "Build completed successfully!" -ForegroundColor Green
        Write-Host "====================================" -ForegroundColor Green
        
        $dllInfo = Get-Item $dllPath
        Write-Host "DLL file: $($dllInfo.FullName)" -ForegroundColor Green
        Write-Host "File size: $([math]::Round($dllInfo.Length / 1KB, 2)) KB" -ForegroundColor Green
        Write-Host "Modified: $($dllInfo.LastWriteTime)" -ForegroundColor Green
        
        if (Test-Path $tlbPath) {
            $tlbInfo = Get-Item $tlbPath
            Write-Host "Type library: $($tlbInfo.FullName)" -ForegroundColor Green
        }
        
        # COM component registration status
        if ($Configuration -eq "Debug" -and -not $NoRegister) {
            Write-Host ""
            Write-Host "Note: COM component automatically registered (Debug mode)" -ForegroundColor Cyan
        } elseif ($NoRegister) {
            Write-Host ""
            Write-Host "Note: COM component registration skipped" -ForegroundColor Yellow
        }
        
        # Provide manual registration commands
        Write-Host ""
        Write-Host "Manual COM component registration commands:" -ForegroundColor Cyan
        Write-Host "Register:   regsvr32 `"$($dllInfo.FullName)`"" -ForegroundColor White
        Write-Host "Unregister: regsvr32 /u `"$($dllInfo.FullName)`"" -ForegroundColor White
        Write-Host "Or use:     cmake --build . --target unregister_com" -ForegroundColor White
        
    } else {
        throw "Build completed but output file not found: $dllPath"
    }

} catch {
    Write-Host ""
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Provide troubleshooting suggestions
    Write-Host ""
    Write-Host "Troubleshooting suggestions:" -ForegroundColor Yellow
    Write-Host "1. Ensure PowerShell is running as Administrator" -ForegroundColor White
    Write-Host "2. Check Visual Studio 2022 is properly installed" -ForegroundColor White
    Write-Host "3. Ensure Windows SDK is installed (includes MIDL compiler)" -ForegroundColor White
    Write-Host "4. Ensure MFC components are installed" -ForegroundColor White
    Write-Host "5. Try using -Clean parameter for clean rebuild" -ForegroundColor White
    
    exit 1
} finally {
    # Return to original directory
    Set-Location ".."
}

Write-Host ""
Write-Host "Build script execution completed." -ForegroundColor Green 