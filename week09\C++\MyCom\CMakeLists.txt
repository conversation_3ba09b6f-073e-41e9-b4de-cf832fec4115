cmake_minimum_required(VERSION 3.20)

# 设置项目名称和语言，添加对RC资源编译的支持
project(MyCom LANGUAGES CXX C RC)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 配置类型检查
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Build type" FORCE)
endif()

# 查找MFC
set(CMAKE_MFC_FLAG 2)  # 使用动态MFC库

# 定义IDL文件处理函数
function(process_idl_file IDL_FILE)
    get_filename_component(IDL_NAME ${IDL_FILE} NAME_WE)
    set(IDL_HEADER "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}_i.h")
    set(IDL_IID "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}_i.c")
    set(IDL_PROXY "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}_p.c")
    set(IDL_TLB "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}.tlb")
    
    # 根据平台设置MIDL参数
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(MIDL_TARGET_ENV "win64")
    else()
        set(MIDL_TARGET_ENV "win32")
    endif()
    
    # 设置MIDL预处理器定义
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(MIDL_DEFINES "/D_DEBUG")
    else()
        set(MIDL_DEFINES "/DNDEBUG")
    endif()
    
    add_custom_command(
        OUTPUT ${IDL_HEADER} ${IDL_IID} ${IDL_PROXY} ${IDL_TLB}
        COMMAND midl.exe 
            /nologo
            /env ${MIDL_TARGET_ENV}
            /h ${IDL_HEADER}
            /iid ${IDL_IID}
            /proxy ${IDL_PROXY}
            /tlb ${IDL_TLB}
            /Oicf
            /robust
            ${MIDL_DEFINES}
            ${IDL_FILE}
        DEPENDS ${IDL_FILE}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Processing IDL file ${IDL_FILE}"
    )
    
    # 设置生成的文件为父级变量
    set(IDL_GENERATED_FILES ${IDL_HEADER} ${IDL_IID} ${IDL_PROXY} ${IDL_TLB} PARENT_SCOPE)
endfunction()

# 处理IDL文件
process_idl_file(MyCom.idl)

# 定义源文件
set(HEADER_FILES
    compreg.h
    dllmain.h
    framework.h
    pch.h
    Resource.h
    targetver.h
    xdlldata.h
    ${CMAKE_CURRENT_BINARY_DIR}/MyCom_i.h
)

set(SOURCE_FILES
    compreg.cpp
    dllmain.cpp
    MyCom.cpp
    pch.cpp
    xdlldata.c
    ${CMAKE_CURRENT_BINARY_DIR}/MyCom_i.c
    ${CMAKE_CURRENT_BINARY_DIR}/MyCom_p.c
)

set(RESOURCE_FILES
    MyCom.rc
)

set(OTHER_FILES
    MyCom.def
    MyCom.rgs
    MyCom.idl
)

# 创建DLL目标
add_library(MyCom SHARED 
    ${SOURCE_FILES}
    ${HEADER_FILES}
    ${RESOURCE_FILES}
    ${OTHER_FILES}
)

# 添加IDL生成的文件依赖
add_custom_target(MyCom_IDL_Files DEPENDS ${IDL_GENERATED_FILES})
add_dependencies(MyCom MyCom_IDL_Files)

# 设置预编译头
target_precompile_headers(MyCom PRIVATE 
    "$<$<COMPILE_LANGUAGE:CXX>:pch.h>"
)

# 排除某些文件使用预编译头
set_source_files_properties(
    dllmain.cpp
    ${CMAKE_CURRENT_BINARY_DIR}/MyCom_i.c
    ${CMAKE_CURRENT_BINARY_DIR}/MyCom_p.c
    xdlldata.c
    PROPERTIES 
    SKIP_PRECOMPILE_HEADERS ON
)

# 设置编译器定义
target_compile_definitions(MyCom PRIVATE
    _WINDOWS
    _USRDLL
    _MERGE_PROXYSTUB
    UNICODE
    _UNICODE
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
    $<$<PLATFORM_ID:Win32>:WIN32>
)

# 设置编译选项
if(MSVC)
    target_compile_options(MyCom PRIVATE
        /W3  # 警告级别3
        $<$<CONFIG:Debug>:/Od>  # Debug: 禁用优化
        $<$<CONFIG:Release>:/O2>  # Release: 最大速度优化
        /sdl  # SDL检查
    )
    
    # 设置链接选项
    target_link_options(MyCom PRIVATE
        /SUBSYSTEM:WINDOWS
        /DEF:${CMAKE_CURRENT_SOURCE_DIR}/MyCom.def
        $<$<CONFIG:Release>:/OPT:REF>
        $<$<CONFIG:Release>:/OPT:ICF>
    )
endif()

# 包含目录
target_include_directories(MyCom PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
)

# 链接库
target_link_libraries(MyCom PRIVATE
    comsvcs.lib
)

# 设置输出名称
set_target_properties(MyCom PROPERTIES
    OUTPUT_NAME "MyCom"
    SUFFIX ".dll"
)

# 资源编译设置
if(MSVC)
    set_source_files_properties(MyCom.rc PROPERTIES 
        COMPILE_FLAGS "/l0x409 /I${CMAKE_CURRENT_BINARY_DIR}"
    )
endif()

# 后构建步骤：注册DLL（仅在Debug模式下）
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_custom_command(TARGET MyCom POST_BUILD
        COMMAND regsvr32 /s "$<TARGET_FILE:MyCom>"
        COMMENT "Registering COM component"
        VERBATIM
    )
endif()

# 添加清理目标来注销COM组件
add_custom_target(unregister_com
    COMMAND regsvr32 /s /u "$<TARGET_FILE:MyCom>"
    COMMENT "Unregistering COM component"
    DEPENDS MyCom
)

# 创建安装规则
install(TARGETS MyCom
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/MyCom.tlb
    DESTINATION bin
) 